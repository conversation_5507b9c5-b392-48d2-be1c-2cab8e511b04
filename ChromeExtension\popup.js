document.addEventListener('DOMContentLoaded', function () {
    // JSON File Reader elements
    // Url: https://dichvucong.dancuquocgia.gov.vn/portal/p/home/<USER>
    const fileInput = document.getElementById('jsonFileInput');
    const fileName = document.getElementById('fileName');
    const jsonDisplay = document.getElementById('jsonDisplay');
    const errorMessage = document.getElementById('errorMessage');
    const clearBtn = document.getElementById('clearBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const rawTab = document.getElementById('rawTab');
    const formattedTab = document.getElementById('formattedTab');
    const treeTab = document.getElementById('treeTab');
    const statsSection = document.getElementById('statsSection');
    const fileSize = document.getElementById('fileSize');
    const objectCount = document.getElementById('objectCount');
    const arrayCount = document.getElementById('arrayCount');

    // Load persisted data when popup opens
    loadPersistedData();

    // File input change handler
    fileInput.addEventListener('change', function (event) {
        const file = event.target.files[0];
        if (file) {
            if (file.type === 'application/json' || file.name.endsWith('.json')) {
                readJsonFile(file);
            } else {
                showError('Please select a valid JSON file.');
            }
        }
    });

    // Tab switching
    rawTab.addEventListener('click', () => switchTab('raw'));
    formattedTab.addEventListener('click', () => switchTab('formatted'));
    treeTab.addEventListener('click', () => switchTab('tree'));

    // Clear button
    clearBtn.addEventListener('click', clearData);

    // Process button
    processBtn.addEventListener('click', processJson);

    function readJsonFile(file) {
        const reader = new FileReader();

        reader.onload = function (e) {
            try {
                const jsonText = e.target.result.replace(/\/\/.*$/gm, '')         // Remove single-line comments
                    .replace(/\/\*[\s\S]*?\*\//gm, ''); // Remove block comments
                const jsonData = JSON.parse(jsonText);

                currentJsonData = jsonData;
                currentFileName = file.name;

                // Save to storage for persistence
                saveToStorage(jsonData, file.name);

                displayFileName(file.name);
                displayJsonData(jsonData, jsonText);
                showStats(file, jsonData);
                enableButtons();
                hideError();

            } catch (error) {
                showError('Invalid JSON file: ' + error.message);
                clearData();
            }
        };

        reader.onerror = function () {
            showError('Error reading file.');
            clearData();
        };

        reader.readAsText(file);
    }

    function displayFileName(name) {
        fileName.textContent = name;
        fileName.style.display = 'block';
    }

    function displayJsonData(data, rawText) {
        // Default to formatted view
        switchTab('formatted');
        updateJsonDisplay(data, rawText);
    }

    function updateJsonDisplay(data, rawText) {
        const activeTab = document.querySelector('.tab-btn.active').id;

        switch (activeTab) {
            case 'rawTab':
                jsonDisplay.innerHTML = `<pre class="json-raw">${escapeHtml(rawText)}</pre>`;
                break;
            case 'formattedTab':
                jsonDisplay.innerHTML = `<pre class="json-formatted">${escapeHtml(JSON.stringify(data, null, 2))}</pre>`;
                break;
            case 'treeTab':
                jsonDisplay.innerHTML = createTreeView(data);
                break;
        }
    }

    function createTreeView(obj, level = 0) {
        let html = '';
        const indent = '  '.repeat(level);

        if (Array.isArray(obj)) {
            html += `<div class="tree-array">[</div>`;
            obj.forEach((item, index) => {
                html += `<div class="tree-item" style="margin-left: ${(level + 1) * 20}px;">`;
                html += `<span class="tree-index">${index}:</span> `;
                if (typeof item === 'object' && item !== null) {
                    html += createTreeView(item, level + 1);
                } else {
                    html += `<span class="tree-value tree-${typeof item}">${escapeHtml(JSON.stringify(item))}</span>`;
                }
                html += `</div>`;
            });
            html += `<div class="tree-array">]</div>`;
        } else if (typeof obj === 'object' && obj !== null) {
            html += `<div class="tree-object">{</div>`;
            Object.entries(obj).forEach(([key, value]) => {
                html += `<div class="tree-item" style="margin-left: ${(level + 1) * 20}px;">`;
                html += `<span class="tree-key">"${escapeHtml(key)}":</span> `;
                if (typeof value === 'object' && value !== null) {
                    html += createTreeView(value, level + 1);
                } else {
                    html += `<span class="tree-value tree-${typeof value}">${escapeHtml(JSON.stringify(value))}</span>`;
                }
                html += `</div>`;
            });
            html += `<div class="tree-object">}</div>`;
        } else {
            html += `<span class="tree-value tree-${typeof obj}">${escapeHtml(JSON.stringify(obj))}</span>`;
        }

        return html;
    }

    function switchTab(tabType) {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(tab => tab.classList.remove('active'));

        // Add active class to selected tab
        const tabMap = {
            'raw': rawTab,
            'formatted': formattedTab,
            'tree': treeTab
        };

        tabMap[tabType].classList.add('active');

        // Update display if we have data
        if (currentJsonData) {
            updateJsonDisplay(currentJsonData, JSON.stringify(currentJsonData));
        }
    }

    function showStats(file, data) {
        fileSize.textContent = formatFileSize(file.size);

        const stats = analyzeJson(data);
        objectCount.textContent = stats.objects;
        arrayCount.textContent = stats.arrays;

        statsSection.style.display = 'block';
    }

    function analyzeJson(obj) {
        let objects = 0;
        let arrays = 0;

        function analyze(item) {
            if (Array.isArray(item)) {
                arrays++;
                item.forEach(analyze);
            } else if (typeof item === 'object' && item !== null) {
                objects++;
                Object.values(item).forEach(analyze);
            }
        }

        analyze(obj);
        return { objects, arrays };
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function enableButtons() {
        clearBtn.disabled = false;
        processBtn.disabled = false;
    }

    function clearData() {
        currentJsonData = null;
        currentFileName = '';
        fileInput.value = '';
        fileName.style.display = 'none';
        jsonDisplay.innerHTML = `
            <div class="placeholder">
                <p>No JSON file selected</p>
                <p class="placeholder-hint">Choose a JSON file to view its contents</p>
            </div>
        `;
        statsSection.style.display = 'none';
        clearBtn.disabled = true;
        processBtn.disabled = true;
        hideError();

        // Clear from storage as well
        clearFromStorage();
    }

    function processJson() {
        console.log(currentJsonData);

        // Get the current active tab and check if it contains the required string
        chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
            const currentTab = tabs[0];
            const requiredString = "thong-bao-luu-tru.html?ma_thu_tuc=2.001159";

            // Check if the current tab URL contains the required string
            if (currentTab.url && currentTab.url.includes(requiredString)) {
                // URL contains the required string, proceed with processing
               
                chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    function: executeFormFilling,
                    args: [currentJsonData]
                });              

            } else {
                // URL doesn't contain the required string, show error
                showError("Input form not found");
            }
        });
    }

    // Wrapper function that contains both main form and member functions
    function executeFormFilling(currentJsonData) {
        // Helper function to create delays
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Define setDataForMember function inside the injected context
        async function setDataForMember(member) {
            // Add your member data filling logic here
            console.log(new Date().toISOString(), ': Setting data for member:', member);
            // Find the citizen name input with id "guest_txtCITIZENNAME"
            const citizenNameInput = document.querySelector('input[id="guest_txtCITIZENNAME"]');
            if (citizenNameInput) {
                citizenNameInput.value = member.Name;
                console.log('Successfully set citizenNameInput to value: ', citizenNameInput.value);
            }
            else {
                console.log('CitizenName input element not found');
                return;
            }
            // Find the DOB input with id "guest_txtDOB"
            const guestDobInput = document.querySelector('input[id="guest_txtDOB"]');
            if (guestDobInput) {
                guestDobInput.value = ""
                guestDobInput.value = member.DOB;
                // Dispatch events to trigger any listeners
                const events = ['input', 'change', 'blur', 'keyup'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    guestDobInput.dispatchEvent(event);
                });
                console.log('Successfully set GuestDobInput to value: ', guestDobInput.value);
            }
            else {
                console.log('GuestDob input element not found');
                return;
            }
            // Find the gender select with id "guest_cboGENDER_ID"
            const genderSelect = document.getElementById("guest_cboGENDER_ID");
            const genderOptions = genderSelect.options;
            for (let i = 0; i < genderOptions.length; i++) {
                if (genderOptions[i].text.toLowerCase() === member.Gender.toLowerCase()) {
                    genderSelect.selectedIndex = i;
                    const changeEvent = new Event('change', { bubbles: true });
                    genderSelect.dispatchEvent(changeEvent)
                    break;
                }
            }
            // Find the ID card input with id "guest_txtIDCARD_NUMBER"
            const guestIdCardInput = document.querySelector('input[id="guest_txtIDCARD_NUMBER"]');
            if (guestIdCardInput) {
                guestIdCardInput.value = member.IdNumber;
                console.log('Successfully set guestIdCardInput to value: ', guestIdCardInput.value);
            }
            else {
                console.log('GuestIdCard input element not found');
                return;
            }
            // Find the address type radio with name "guest_radADDRESS_TYPE" and value "1" (Tạm trú)
            const addressTypeRadios = document.querySelectorAll('input[name="guest_radADDRESS_TYPE"]')
            addressTypeRadios.forEach(radio => {
                if (radio.value === '1') {
                    radio.checked = true;
                    const changeEvent = new Event('change', { bubbles: true });
                    radio.dispatchEvent(changeEvent)
                }
            })
            // Find the reason input with id "guest_txtREASON"
            const reasonInputs = document.querySelector('textarea[id="guest_txtREASON"]')
            if (reasonInputs) {
                reasonInputs.value = "Lao động, học tập và sinh sống";
                console.log('Successfully set reasonInputs to value: ', reasonInputs.value);
            }
            else {
                console.log('ReasonInputs input element not found');
                return;
            }
            // Find the start date input with id "guest_txtSTART_DATE"
            const startDateInputs = document.querySelector('input[id="guest_txtSTART_DATE"]')
            if (startDateInputs) {
                const today = new Date();
                today.setDate(today.getDate() + currentJsonData.StartDaysFromToday);
                const startDay = String(today.getDate()).padStart(2, '0');
                const startMonth = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
                const startYear = today.getFullYear();
                startDateInputs.value = ""
                // Dispatch events to trigger any listeners
                const events = ['input', 'change', 'blur', 'keyup'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    startDateInputs.dispatchEvent(event);
                });
                startDateInputs.value = `${startDay}/${startMonth}/${startYear}`;
                // Dispatch events to trigger any listeners
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    startDateInputs.dispatchEvent(event);
                });
                console.log('Successfully set StartDateInputs to value: ', startDateInputs.value);
            }
            else {
                console.log('StartDate input element not found');
                return;
            }

            // Find the end date input with id "guest_txtEND_DATE"
            const endDateInputs = document.querySelector('input[id="guest_txtEND_DATE"]')
            if (endDateInputs) {
                const today = new Date();
                today.setDate(today.getDate() + currentJsonData.StartDaysFromToday + 29);
                const endDay = String(today.getDate()).padStart(2, '0');
                const endMonth = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
                const endYear = today.getFullYear();
                endDateInputs.value = ""
                endDateInputs.value = `${endDay}/${endMonth}/${endYear}`;
                // Dispatch events to trigger any listeners
                const events = ['input', 'change', 'blur', 'keyup'];
                events.forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    endDateInputs.dispatchEvent(event);
                });
                console.log('Successfully set EndDateInputs to value: ', endDateInputs.value);
            }
            else {
                console.log('EndDate input element not found');
                return;
            }
            // Find the room number input with id "guest_txtROOM"
            const roomNumberInput = document.querySelector('input[id="guest_txtROOM"]')
            if (roomNumberInput) {
                roomNumberInput.value = member.RoomNumber;
                console.log('Successfully set RoomNumberInput to value: ', roomNumberInput.value);
            }
            else {
                console.log('RoomNumber input element not found');
                return;
            }

            // Click save button
            await delay(1000);
            const saveButton = document.querySelector('button[id="btnSaveNLT"]');
            if (saveButton) {
                saveButton.click();
                console.log(new Date().toISOString(), ': Successfully clicked btnSave button to save form');

                await delay(1000);
                // const okButton = document.querySelector('button[id="alertify-ok"]');
                // if (okButton) {
                //     okButton.click();
                //     console.log(new Date().toISOString(), ': Successfully clicked btnOK button to confirm form submission');
                // } else {
                //     console.log('Button with id "btnOK" not found');
                // }

            } else {
                console.log('Button with id "btnSave" not found');
            }

        }

        // Define setDataOnMainForm function inside the injected context
        async function setDataOnMainForm(currentJsonData, setDataForMemberCallback) {
            // Find the select input with id "accomStay_cboPROVINCE_ID"
            const selectElement = document.querySelector('select[id="accomStay_cboPROVINCE_ID"]');

            if (selectElement) {
                // Set the value to 20
                selectElement.value = currentJsonData.ProvinceId;

                // Trigger change event in case there are listeners
                const changeEvent = new Event('change', { bubbles: true });
                selectElement.dispatchEvent(changeEvent);

                console.log('Successfully set accomStay_cboPROVINCE_ID to value: ', selectElement.value);

                // Wait a bit for the change event to populate the address select, then select the last item
                await delay(500);
                {
                    // Find the address select input with id "accomStay_cboADDRESS_ID"
                    const addressSelect = document.querySelector('select[id="accomStay_cboADDRESS_ID"]');
                    if (addressSelect && addressSelect.options.length > 0) {
                        // Get all option values
                        const optionValues = Array.from(addressSelect.options).map(option => option.value);
                        console.log('Address select option values:', optionValues);
                        if (optionValues.includes(currentJsonData.AddressId)) {
                            // Select the  item
                            addressSelect.value = currentJsonData.AddressId;
                            // Trigger change event for the address select
                            const addressChangeEvent = new Event('change', { bubbles: true });
                            addressSelect.dispatchEvent(addressChangeEvent);

                            console.log('Successfully selected last item in accomStay_cboADDRESS_ID:', addressSelect.value);
                        }
                        else {
                            console.log('AddressId not found in address select options');
                            window.alert('AddressId not found in address select options');
                            return;
                        }
                    } else {
                        console.log('Address select element not found or has no options');
                        window.alert('Address select element not found or has no options');
                        return;
                    }

                    // Find the accommodation type select input with id "accomStay_cboACCOMMODATION_TYPE"
                    const accommodationTypeSelect = document.querySelector('select[id="accomStay_cboACCOMMODATION_TYPE"]');
                    if (accommodationTypeSelect && accommodationTypeSelect.options.length > 0) {
                        const optionValues = Array.from(accommodationTypeSelect.options).map(option => option.value);
                        console.log('AccommodationType select option values:', optionValues);
                        if (optionValues.includes(currentJsonData.AccommodationType)) {
                            accommodationTypeSelect.value = currentJsonData.AccommodationType;
                            const accommodationTypeChangeEvent = new Event('change', { bubbles: true });
                            accommodationTypeSelect.dispatchEvent(accommodationTypeChangeEvent);
                            console.log('Successfully selected AccommodationType in accomStay_cboACCOMMODATION_TYPE:', accommodationTypeSelect.value);
                        }
                        else {
                            console.log('AccommodationType not found in accommodationType select options');
                            window.alert('AccommodationType not found in accommodationType select options');
                            return;
                        }
                    }
                    else {
                        console.log('AccommodationType select element not found or has no options');
                        window.alert('AccommodationType select element not found or has no options');
                        return;
                    }
                    // Find the accommodation name input with id "accomStay_txtNAME_T"
                    const accommodationNameInput = document.querySelector('input[id="accomStay_txtNAME_T"]');
                    if (accommodationNameInput) {
                        accommodationNameInput.value = currentJsonData.AccommodationName;
                        console.log('Successfully set accommodationNameInput to value: ', accommodationNameInput.value);
                    }
                    else {
                        console.log('AccommodationName input element not found');
                        window.alert('AccommodationName input element not found');
                        return;
                    }
                    // Find the accommodation address input with id "accomStay_txtADDRESS"
                    const accommodationAddressNameInput = document.querySelector('input[id="accomStay_txtADDRESS"]');
                    if (accommodationAddressNameInput) {
                        accommodationAddressNameInput.value = currentJsonData.AccommodationAddress;
                        console.log('Successfully set accommodationAddressNameInput to value: ', accommodationAddressNameInput.value);
                    }
                    else {
                        console.log('AccommodationAddress input element not found');
                        window.alert('AccommodationAddress input element not found');
                        return;
                    }
                    // Find the check liability checkbox with id "chkCHECK_LIABILITY"
                    const checkLiabilityCheckbox = document.querySelector('input[id="chkCHECK_LIABILITY"]');
                    if (checkLiabilityCheckbox) {
                        checkLiabilityCheckbox.checked = true;
                        console.log('Successfully set checkLiabilityCheckbox to value: ', checkLiabilityCheckbox.checked);
                    }
                    else {
                        console.log('CheckLiabilityCheckbox input element not found');
                        window.alert('CheckLiabilityCheckbox input element not found');
                        return;
                    }
                    // Click the button to open popup form after all data is set
                    if (currentJsonData.Members && currentJsonData.Members.length > 0) {
                        // Process members synchronously one by one
                        for (let i = 0; i < currentJsonData.Members.length; i++) {
                            const member = currentJsonData.Members[i];

                            // Wait before processing each member
                            await delay(1000);

                            const addPersonButton = document.querySelector('a[id="btnAddPersonLT"]');
                            if (addPersonButton) {
                                addPersonButton.click();
                                console.log(new Date().toISOString(), ':Successfully clicked btnAddPersonLT button to open popup form');

                                // Wait for the popup to open, then process member data
                                await delay(1000);
                                await setDataForMemberCallback(member);
                            } else {
                                console.log('Button with id "btnAddPersonLT" not found');
                                window.alert('Button with id "btnAddPersonLT" not found');
                                break; // Exit the loop if button is not found
                            }
                        }
                    }
                } // End of the block that replaced setTimeout

            } else {
                console.log('Select element with id "accomStay_cboPROVINCE_ID" not found');
                window.alert('Select element with id "accomStay_cboPROVINCE_ID" not found');
                return;
            }
        }

        // Execute the main form filling with the member callback
        setDataOnMainForm(currentJsonData, setDataForMember).catch(error => {
            console.error('Error in setDataOnMainForm:', error);
        });
    }

    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.style.display = 'block';
    }

    function hideError() {
        errorMessage.style.display = 'none';
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Storage functions to persist data across popup sessions
    function saveToStorage(jsonData, fileName) {
        chrome.storage.session.set({
            'currentJsonData': jsonData,
            'currentFileName': fileName
        });
    }

    function loadPersistedData() {
        chrome.storage.session.get(['currentJsonData', 'currentFileName'], function (result) {
            if (result.currentJsonData) {
                currentJsonData = result.currentJsonData;
                currentFileName = result.currentFileName || '';

                displayFileName(currentFileName);
                displayJsonData(currentJsonData, JSON.stringify(currentJsonData));
                showStats({ size: JSON.stringify(currentJsonData).length }, currentJsonData);
                enableButtons();
                hideError();
            }
        });
    }

    function clearFromStorage() {
        chrome.storage.session.remove(['currentJsonData', 'currentFileName']);
    }
});
