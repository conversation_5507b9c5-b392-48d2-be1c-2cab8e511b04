document.addEventListener('DOMContentLoaded', function () {
    const fileInput = document.getElementById('jsonFileInput');
    const fileName = document.getElementById('fileName');
    const jsonDisplay = document.getElementById('jsonDisplay');
    const errorMessage = document.getElementById('errorMessage');
    const clearBtn = document.getElementById('clearBtn');
    const processBtn = document.getElementById('processBtn');
    const rawTab = document.getElementById('rawTab');
    const formattedTab = document.getElementById('formattedTab');
    const treeTab = document.getElementById('treeTab');
    const statsSection = document.getElementById('statsSection');
    const fileSize = document.getElementById('fileSize');
    const objectCount = document.getElementById('objectCount');
    const arrayCount = document.getElementById('arrayCount');

    let currentJsonData = null;
    let currentFileName = '';

    // Load persisted data when popup opens
    loadPersistedData();

    // File input change handler
    fileInput.addEventListener('change', function (event) {
        const file = event.target.files[0];
        if (file) {
            if (file.type === 'application/json' || file.name.endsWith('.json')) {
                readJsonFile(file);
            } else {
                showError('Please select a valid JSON file.');
            }
        }
    });

    // Tab switching
    rawTab.addEventListener('click', () => switchTab('raw'));
    formattedTab.addEventListener('click', () => switchTab('formatted'));
    treeTab.addEventListener('click', () => switchTab('tree'));

    // Clear button
    clearBtn.addEventListener('click', clearData);

    // Process button
    processBtn.addEventListener('click', processJson);

    function readJsonFile(file) {
        const reader = new FileReader();

        reader.onload = function (e) {
            try {
                const jsonText = e.target.result.replace(/\/\/.*$/gm, '')         // Remove single-line comments
                    .replace(/\/\*[\s\S]*?\*\//gm, ''); // Remove block comments
                const jsonData = JSON.parse(jsonText);

                currentJsonData = jsonData;
                currentFileName = file.name;

                // Save to storage for persistence
                saveToStorage(jsonData, file.name);

                displayFileName(file.name);
                displayJsonData(jsonData, jsonText);
                showStats(file, jsonData);
                enableButtons();
                hideError();

            } catch (error) {
                showError('Invalid JSON file: ' + error.message);
                clearData();
            }
        };

        reader.onerror = function () {
            showError('Error reading file.');
            clearData();
        };

        reader.readAsText(file);
    }

    function displayFileName(name) {
        fileName.textContent = name;
        fileName.style.display = 'block';
    }

    function displayJsonData(data, rawText) {
        // Default to formatted view
        switchTab('formatted');
        updateJsonDisplay(data, rawText);
    }

    function updateJsonDisplay(data, rawText) {
        const activeTab = document.querySelector('.tab-btn.active').id;

        switch (activeTab) {
            case 'rawTab':
                jsonDisplay.innerHTML = `<pre class="json-raw">${escapeHtml(rawText)}</pre>`;
                break;
            case 'formattedTab':
                jsonDisplay.innerHTML = `<pre class="json-formatted">${escapeHtml(JSON.stringify(data, null, 2))}</pre>`;
                break;
            case 'treeTab':
                jsonDisplay.innerHTML = createTreeView(data);
                break;
        }
    }

    function createTreeView(obj, level = 0) {
        let html = '';
        const indent = '  '.repeat(level);

        if (Array.isArray(obj)) {
            html += `<div class="tree-array">[</div>`;
            obj.forEach((item, index) => {
                html += `<div class="tree-item" style="margin-left: ${(level + 1) * 20}px;">`;
                html += `<span class="tree-index">${index}:</span> `;
                if (typeof item === 'object' && item !== null) {
                    html += createTreeView(item, level + 1);
                } else {
                    html += `<span class="tree-value tree-${typeof item}">${escapeHtml(JSON.stringify(item))}</span>`;
                }
                html += `</div>`;
            });
            html += `<div class="tree-array">]</div>`;
        } else if (typeof obj === 'object' && obj !== null) {
            html += `<div class="tree-object">{</div>`;
            Object.entries(obj).forEach(([key, value]) => {
                html += `<div class="tree-item" style="margin-left: ${(level + 1) * 20}px;">`;
                html += `<span class="tree-key">"${escapeHtml(key)}":</span> `;
                if (typeof value === 'object' && value !== null) {
                    html += createTreeView(value, level + 1);
                } else {
                    html += `<span class="tree-value tree-${typeof value}">${escapeHtml(JSON.stringify(value))}</span>`;
                }
                html += `</div>`;
            });
            html += `<div class="tree-object">}</div>`;
        } else {
            html += `<span class="tree-value tree-${typeof obj}">${escapeHtml(JSON.stringify(obj))}</span>`;
        }

        return html;
    }

    function switchTab(tabType) {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-btn').forEach(tab => tab.classList.remove('active'));

        // Add active class to selected tab
        const tabMap = {
            'raw': rawTab,
            'formatted': formattedTab,
            'tree': treeTab
        };

        tabMap[tabType].classList.add('active');

        // Update display if we have data
        if (currentJsonData) {
            updateJsonDisplay(currentJsonData, JSON.stringify(currentJsonData));
        }
    }

    function showStats(file, data) {
        fileSize.textContent = formatFileSize(file.size);

        const stats = analyzeJson(data);
        objectCount.textContent = stats.objects;
        arrayCount.textContent = stats.arrays;

        statsSection.style.display = 'block';
    }

    function analyzeJson(obj) {
        let objects = 0;
        let arrays = 0;

        function analyze(item) {
            if (Array.isArray(item)) {
                arrays++;
                item.forEach(analyze);
            } else if (typeof item === 'object' && item !== null) {
                objects++;
                Object.values(item).forEach(analyze);
            }
        }

        analyze(obj);
        return { objects, arrays };
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function enableButtons() {
        clearBtn.disabled = false;
        processBtn.disabled = false;
    }

    function clearData() {
        currentJsonData = null;
        currentFileName = '';
        fileInput.value = '';
        fileName.style.display = 'none';
        jsonDisplay.innerHTML = `
            <div class="placeholder">
                <p>No JSON file selected</p>
                <p class="placeholder-hint">Choose a JSON file to view its contents</p>
            </div>
        `;
        statsSection.style.display = 'none';
        clearBtn.disabled = true;
        processBtn.disabled = true;
        hideError();

        // Clear from storage as well
        clearFromStorage();
    }

    function processJson() {
        console.log(currentJsonData);

        // Get the current active tab and check if it contains the required string
        chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
            const currentTab = tabs[0];
            const requiredString = "thong-bao-luu-tru.html?ma_thu_tuc=2.001159";

            // Check if the current tab URL contains the required string
            if (currentTab.url && currentTab.url.includes(requiredString)) {
                // URL contains the required string, proceed with processing
                chrome.scripting.executeScript({
                    target: { tabId: currentTab.id },
                    function: (currentJsonData) => {
                        setProvinceSelect(currentJsonData)
                    },
                    args: [currentJsonData]
                });
            } else {
                // URL doesn't contain the required string, show error
                showError("Input form not found");
            }
        });
    }

    // Function to be injected into the active tab
    function setProvinceSelect(currentJsonData) {
        // Find the select input with id "accomStay_cboPROVINCE_ID"
        const selectElement = document.querySelector('select[id="accomStay_cboPROVINCE_ID"]');

        if (selectElement) {
            // Set the value to 20
            selectElement.value = currentJsonData.ProvinceId;

            // Trigger change event in case there are listeners
            const changeEvent = new Event('change', { bubbles: true });
            selectElement.dispatchEvent(changeEvent);

            console.log('Successfully set accomStay_cboPROVINCE_ID to value: ', selectElement.value);

            // Wait a bit for the change event to populate the address select, then select the last item
            setTimeout(() => {
                // Find the address select input with id "accomStay_cboADDRESS_ID"
                const addressSelect = document.querySelector('select[id="accomStay_cboADDRESS_ID"]');
                if (addressSelect && addressSelect.options.length > 0) {
                    // Get all option values
                    const optionValues = Array.from(addressSelect.options).map(option => option.value);
                    console.log('Address select option values:', optionValues);
                    if (optionValues.includes(currentJsonData.AddressId)) {
                        // Select the  item
                        addressSelect.value = currentJsonData.AddressId;
                        // Trigger change event for the address select
                        const addressChangeEvent = new Event('change', { bubbles: true });
                        addressSelect.dispatchEvent(addressChangeEvent);

                        console.log('Successfully selected last item in accomStay_cboADDRESS_ID:', addressSelect.value);
                    }
                    else{
                        console.log('AddressId not found in address select options');
                        showError("AddressId not found in address select options");
                        return;
                    }
                } else {
                    console.log('Address select element not found or has no options');
                    showError("Address select element not found or has no options");
                    return;
                }

                // Find the accommodation type select input with id "accomStay_cboACCOMMODATION_TYPE"
                const accommodationTypeSelect = document.querySelector('select[id="accomStay_cboACCOMMODATION_TYPE"]');
                if (accommodationTypeSelect && accommodationTypeSelect.options.length > 0) {
                    const optionValues = Array.from(addressSelect.options).map(option => option.value);
                    console.log('AccommodationType select option values:', optionValues);
                    if (optionValues.includes(currentJsonData.AccommodationType)) {
                        accommodationTypeSelect.value = currentJsonData.AccommodationType;
                        const accommodationTypeChangeEvent = new Event('change', { bubbles: true });
                        accommodationTypeSelect.dispatchEvent(accommodationTypeChangeEvent);
                        console.log('Successfully selected AccommodationType in accomStay_cboACCOMMODATION_TYPE:', accommodationTypeSelect.value);
                    }
                    else{
                        console.log('AccommodationType not found in accommodationType select options');
                        showError("AccommodationType not found in accommodationType select options");
                        return;
                    }
                }
                else{
                    console.log('AccommodationType select element not found or has no options');
                    showError("AccommodationType select element not found or has no options");
                    return;
                }
                // Find the accommodation name input with id "accomStay_txtNAME_T"
                const accommodationNameInput = document.querySelector('input[id="accomStay_txtNAME_T"]');
                if (accommodationNameInput) {
                    accommodationNameInput.value = currentJsonData.AccommodationName;
                    console.log('Successfully set accommodationNameInput to value: ', accommodationNameInput.value);
                }
                else{
                    console.log('AccommodationName input element not found');
                    showError("AccommodationName input element not found");
                    return;
                } 
                // Find the accommodation address input with id "accomStay_txtADDRESS"
                const accommodationAddressNameInput = document.querySelector('input[id="accomStay_txtADDRESS"]');
                if (accommodationAddressNameInput) {
                    accommodationAddressNameInput.value = currentJsonData.AccommodationAddress;
                    console.log('Successfully set accommodationAddressNameInput to value: ', accommodationAddressNameInput.value);
                }
                else{
                    console.log('AccommodationAddress input element not found');
                    showError("AccommodationAddress input element not found");
                    return;
                }
            }, 500); // Wait 500ms for the province change to populate the address options

        } else {
            console.log('Select element with id "accomStay_cboPROVINCE_ID" not found');
            showError('Select element with id "accomStay_cboPROVINCE_ID" not found');
            return;
        }
    }
    
    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.style.display = 'block';
    }

    function hideError() {
        errorMessage.style.display = 'none';
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Storage functions to persist data across popup sessions
    function saveToStorage(jsonData, fileName) {
        chrome.storage.session.set({
            'currentJsonData': jsonData,
            'currentFileName': fileName
        });
    }

    function loadPersistedData() {
        chrome.storage.session.get(['currentJsonData', 'currentFileName'], function (result) {
            if (result.currentJsonData) {
                currentJsonData = result.currentJsonData;
                currentFileName = result.currentFileName || '';

                displayFileName(currentFileName);
                displayJsonData(currentJsonData, JSON.stringify(currentJsonData));
                showStats({ size: JSON.stringify(currentJsonData).length }, currentJsonData);
                enableButtons();
                hideError();
            }
        });
    }

    function clearFromStorage() {
        chrome.storage.session.remove(['currentJsonData', 'currentFileName']);
    }
});
