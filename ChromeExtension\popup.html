<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON File Reader</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>JSON File Reader</h1>
            <p>Import and read JSON files from your local machine</p>
        </div>
        
        <div class="file-input-section">
            <label for="jsonFileInput" class="file-input-label">
                <span class="file-input-text">Choose JSON File</span>
                <input type="file" id="jsonFileInput" accept=".json" class="file-input">
            </label>
            <div id="fileName" class="file-name"></div>
        </div>
        
        <div class="actions">
            <button id="clearBtn" class="btn btn-secondary" disabled>Clear</button>
            <button id="processBtn" class="btn btn-primary" disabled>Process JSON</button>
        </div>
        
        <div class="content-section">
            <div class="tabs">
                <button id="rawTab" class="tab-btn active">Raw JSON</button>
                <button id="formattedTab" class="tab-btn">Formatted</button>
                <button id="treeTab" class="tab-btn">Tree View</button>
            </div>
            
            <div id="jsonDisplay" class="json-display">
                <div class="placeholder">
                    <p>No JSON file selected</p>
                    <p class="placeholder-hint">Choose a JSON file to view its contents</p>
                </div>
            </div>
        </div>
        
        <div id="errorMessage" class="error-message"></div>
        
        <div class="stats" id="statsSection" style="display: none;">
            <div class="stat-item">
                <span class="stat-label">File Size:</span>
                <span id="fileSize" class="stat-value">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Objects:</span>
                <span id="objectCount" class="stat-value">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Arrays:</span>
                <span id="arrayCount" class="stat-value">-</span>
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
