# JSON File Reader Chrome Extension

A Chrome extension that allows you to import and read JSON files from your local machine with an intuitive interface.

## Features

- 📁 **File Import**: Select and import JSON files from your local machine
- 👁️ **Multiple View Modes**: 
  - Raw JSON view
  - Formatted/Pretty-printed view
  - Interactive tree view
- 📊 **File Statistics**: View file size, object count, and array count
- 💾 **Download**: Re-download the imported JSON file
- 🎨 **Clean Interface**: Modern, responsive design
- ⚡ **Fast Processing**: Instant file parsing and display

## Installation

### Method 1: Load as Unpacked Extension (Development)

1. **Download/Clone** this repository to your local machine
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable Developer Mode** by toggling the switch in the top-right corner
4. **Click "Load unpacked"** button
5. **Select the ChromeExtension folder** containing the extension files
6. The extension should now appear in your extensions list and toolbar

### Method 2: Create PNG Icons (Optional)

For better icon display, you can convert the SVG icon to PNG format:

1. Open `icons/icon.svg` in any graphics editor (like GIMP, Photoshop, or online converters)
2. Export/Save as PNG in the following sizes:
   - `icon16.png` (16x16 pixels)
   - `icon48.png` (48x48 pixels) 
   - `icon128.png` (128x128 pixels)
3. Save these files in the `icons/` directory

## Usage

1. **Click the extension icon** in your Chrome toolbar
2. **Click "Choose JSON File"** to select a JSON file from your computer
3. **View your data** in one of three modes:
   - **Raw**: Original JSON text as-is
   - **Formatted**: Pretty-printed with proper indentation
   - **Tree View**: Interactive hierarchical display
4. **Use the buttons** to:
   - **Clear**: Remove current file and reset the interface
   - **Download JSON**: Save the current JSON data to your computer

## File Structure

```
ChromeExtension/
├── manifest.json          # Extension configuration
├── popup.html            # Main interface HTML
├── popup.css             # Styling
├── popup.js              # JavaScript functionality
├── icons/                # Extension icons
│   └── icon.svg          # SVG icon source
└── README.md             # This file
```

## Technical Details

- **Manifest Version**: 3 (latest Chrome extension standard)
- **Permissions**: Only requires `storage` permission for basic functionality
- **File Types**: Accepts `.json` files and `application/json` MIME type
- **Size Limit**: Handles files up to browser memory limits
- **Browser Support**: Chrome 88+ (Manifest V3 compatible)

## Features in Detail

### File Import
- Drag-and-drop support for JSON files
- File validation to ensure valid JSON format
- Error handling for malformed JSON

### View Modes
- **Raw View**: Shows the original file content exactly as imported
- **Formatted View**: Pretty-prints JSON with proper indentation and syntax highlighting
- **Tree View**: Interactive collapsible tree structure for easy navigation

### Statistics
- File size in human-readable format (Bytes, KB, MB, GB)
- Count of objects and arrays in the JSON structure
- Real-time analysis of JSON structure

## Troubleshooting

### Common Issues

1. **"Invalid JSON file" error**
   - Ensure your file has valid JSON syntax
   - Check for trailing commas, missing quotes, or other syntax errors
   - Use a JSON validator online to verify your file

2. **Extension not loading**
   - Make sure Developer Mode is enabled in Chrome
   - Check that all files are present in the extension directory
   - Look for errors in the Chrome Extensions page

3. **File not displaying**
   - Ensure the file has a `.json` extension
   - Check that the file isn't empty
   - Try with a smaller JSON file first

### Browser Compatibility

This extension is designed for Chrome and Chromium-based browsers that support Manifest V3:
- Chrome 88+
- Microsoft Edge 88+
- Brave Browser
- Other Chromium-based browsers

## Development

To modify or extend this extension:

1. Edit the source files as needed
2. Reload the extension in `chrome://extensions/`
3. Test your changes
4. For distribution, package the extension or publish to Chrome Web Store

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this extension.
