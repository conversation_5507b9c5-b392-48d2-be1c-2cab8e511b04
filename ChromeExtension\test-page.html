<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Datepicker Test Page</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Datepicker CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        .current-value {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-family: monospace;
        }
        .instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chrome Extension Datepicker Test</h1>
        
        <div class="instructions">
            <h5>Instructions:</h5>
            <ol>
                <li>Install the Chrome extension</li>
                <li>Click the extension icon in the toolbar</li>
                <li>Enter a CSS selector (e.g., #guest_txtDOB)</li>
                <li>Choose a date</li>
                <li>Select a method and click "Set Datepicker Value"</li>
            </ol>
        </div>

        <form>
            <div class="form-group">
                <label for="guest_txtDOB" class="form-label">Date of Birth (Bootstrap Datepicker):</label>
                <input type="text" class="form-control datepicker" id="guest_txtDOB" placeholder="Select date">
                <div class="current-value">
                    Current value: <span id="dob-value">None</span>
                </div>
            </div>

            <div class="form-group">
                <label for="other_date" class="form-label">Another Date Field:</label>
                <input type="text" class="form-control datepicker" id="other_date" placeholder="Select date">
                <div class="current-value">
                    Current value: <span id="other-value">None</span>
                </div>
            </div>

            <div class="form-group">
                <label for="regular_date" class="form-label">Regular Date Input (HTML5):</label>
                <input type="date" class="form-control" id="regular_date">
                <div class="current-value">
                    Current value: <span id="regular-value">None</span>
                </div>
            </div>

            <div class="form-group">
                <button type="button" class="btn btn-secondary" onclick="clearAllDates()">Clear All Dates</button>
                <button type="button" class="btn btn-info" onclick="showCurrentValues()">Show Current Values</button>
            </div>
        </form>

        <div class="mt-4">
            <h5>Test Selectors:</h5>
            <ul>
                <li><code>#guest_txtDOB</code> - Main date of birth field</li>
                <li><code>#other_date</code> - Another datepicker field</li>
                <li><code>#regular_date</code> - Regular HTML5 date input</li>
                <li><code>.datepicker</code> - All datepicker fields</li>
            </ul>
        </div>

        <div class="mt-4">
            <h5>Console Output:</h5>
            <div id="console-output" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                Open browser console (F12) to see extension messages...
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Bootstrap Datepicker JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize Bootstrap Datepickers
            $('.datepicker').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true
            });

            // Monitor value changes
            function updateValueDisplay() {
                $('#dob-value').text($('#guest_txtDOB').val() || 'None');
                $('#other-value').text($('#other_date').val() || 'None');
                $('#regular-value').text($('#regular_date').val() || 'None');
            }

            // Update displays when values change
            $('input').on('change input', updateValueDisplay);
            
            // Initial update
            updateValueDisplay();

            // Log to console and page
            const originalLog = console.log;
            const originalWarn = console.warn;
            const originalError = console.error;
            
            function addToConsoleOutput(message, type = 'log') {
                const output = document.getElementById('console-output');
                const timestamp = new Date().toLocaleTimeString();
                const color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black';
                output.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
                output.scrollTop = output.scrollHeight;
            }

            console.log = function(...args) {
                originalLog.apply(console, args);
                addToConsoleOutput(args.join(' '), 'log');
            };

            console.warn = function(...args) {
                originalWarn.apply(console, args);
                addToConsoleOutput(args.join(' '), 'warn');
            };

            console.error = function(...args) {
                originalError.apply(console, args);
                addToConsoleOutput(args.join(' '), 'error');
            };

            console.log('Test page loaded successfully');
            console.log('jQuery version:', $.fn.jquery);
            console.log('Bootstrap Datepicker available:', !!$.fn.datepicker);
        });

        function clearAllDates() {
            $('input[type="text"].datepicker').datepicker('clearDates');
            $('input[type="date"]').val('');
            console.log('All dates cleared');
        }

        function showCurrentValues() {
            const values = {
                guest_txtDOB: $('#guest_txtDOB').val(),
                other_date: $('#other_date').val(),
                regular_date: $('#regular_date').val()
            };
            console.log('Current values:', values);
            alert(JSON.stringify(values, null, 2));
        }

        // Listen for custom events from the extension
        document.addEventListener('setDatepickerValue', function(event) {
            console.log('Received custom event:', event.detail);
        });
    </script>
</body>
</html>
