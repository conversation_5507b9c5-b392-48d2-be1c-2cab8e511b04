<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- JSON brackets -->
  <text x="64" y="75" font-family="monospace" font-size="48" font-weight="bold" fill="white" text-anchor="middle">{}</text>
  
  <!-- Small decorative elements -->
  <circle cx="45" cy="45" r="3" fill="white" opacity="0.8"/>
  <circle cx="83" cy="45" r="3" fill="white" opacity="0.8"/>
  <circle cx="64" cy="95" r="2" fill="white" opacity="0.6"/>
</svg>
