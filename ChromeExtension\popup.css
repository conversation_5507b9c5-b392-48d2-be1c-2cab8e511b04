* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    width: 450px;
    max-height: 600px;
    background: #f8f9fa;
    color: #333;
}

.container {
    padding: 16px;
}

.header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.header h1 {
    font-size: 20px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.header p {
    font-size: 12px;
    color: #6c757d;
}

.file-input-section {
    margin-bottom: 15px;
}

.file-input-label {
    display: block;
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    font-weight: 500;
}

.file-input-label:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.file-input {
    display: none;
}

.file-name {
    margin-top: 8px;
    padding: 8px 12px;
    background: #e8f5e8;
    border-radius: 6px;
    font-size: 12px;
    color: #2d5a2d;
    display: none;
    word-break: break-all;
}

.actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #28a745;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #218838;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

.content-section {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-btn {
    flex: 1;
    padding: 10px 12px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: white;
    color: #495057;
    border-bottom: 2px solid #667eea;
}

.tab-btn:hover:not(.active) {
    background: #e9ecef;
}

.json-display {
    max-height: 300px;
    overflow-y: auto;
    padding: 15px;
    font-size: 11px;
    line-height: 1.4;
}

.placeholder {
    text-align: center;
    color: #6c757d;
    padding: 40px 20px;
}

.placeholder p {
    margin-bottom: 5px;
}

.placeholder-hint {
    font-size: 10px;
    opacity: 0.7;
}

.json-raw, .json-formatted {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    line-height: 1.3;
}

.tree-item {
    margin: 2px 0;
    font-family: 'Courier New', monospace;
}

.tree-key {
    color: #d73a49;
    font-weight: bold;
}

.tree-value {
    font-weight: normal;
}

.tree-string {
    color: #032f62;
}

.tree-number {
    color: #005cc5;
}

.tree-boolean {
    color: #e36209;
}

.tree-null {
    color: #6f42c1;
}

.tree-object, .tree-array {
    color: #24292e;
    font-weight: bold;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    margin-bottom: 15px;
    display: none;
    border: 1px solid #f5c6cb;
}

.stats {
    background: white;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-around;
    font-size: 11px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.stat-value {
    display: block;
    color: #495057;
    font-weight: bold;
    font-size: 12px;
}

/* Scrollbar styling */
.json-display::-webkit-scrollbar {
    width: 6px;
}

.json-display::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.json-display::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.json-display::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
