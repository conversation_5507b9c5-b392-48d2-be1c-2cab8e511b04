<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <h2>Chrome Extension Icon Generator</h2>
    <p>This page helps generate PNG icons from the SVG. Right-click and save each canvas as PNG.</p>
    
    <div>
        <h3>16x16 Icon</h3>
        <canvas id="icon16" width="16" height="16" style="border: 1px solid #ccc; image-rendering: pixelated;"></canvas>
        <button onclick="downloadIcon('icon16', 16)">Download 16x16</button>
    </div>
    
    <div>
        <h3>48x48 Icon</h3>
        <canvas id="icon48" width="48" height="48" style="border: 1px solid #ccc;"></canvas>
        <button onclick="downloadIcon('icon48', 48)">Download 48x48</button>
    </div>
    
    <div>
        <h3>128x128 Icon</h3>
        <canvas id="icon128" width="128" height="128" style="border: 1px solid #ccc;"></canvas>
        <button onclick="downloadIcon('icon128', 128)">Download 128x128</button>
    </div>

    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw border
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = size > 16 ? 2 : 1;
            ctx.stroke();
            
            // Draw JSON text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px monospace`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('{}', size/2, size/2);
            
            // Add decorative dots for larger icons
            if (size > 16) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                const dotSize = size > 48 ? 3 : 2;
                ctx.beginPath();
                ctx.arc(size * 0.35, size * 0.35, dotSize, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.beginPath();
                ctx.arc(size * 0.65, size * 0.35, dotSize, 0, 2 * Math.PI);
                ctx.fill();
                
                if (size > 48) {
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                    ctx.beginPath();
                    ctx.arc(size * 0.5, size * 0.75, 2, 0, 2 * Math.PI);
                    ctx.fill();
                }
            }
        }
        
        function downloadIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Generate all icons on page load
        window.onload = function() {
            createIcon(document.getElementById('icon16'), 16);
            createIcon(document.getElementById('icon48'), 48);
            createIcon(document.getElementById('icon128'), 128);
        };
    </script>
</body>
</html>
