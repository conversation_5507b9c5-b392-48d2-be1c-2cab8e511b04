{"name": "JSON File Reader Test Data", "version": "1.0.0", "description": "Sample JSON data for testing the Chrome extension", "author": {"name": "Developer", "email": "<EMAIL>", "website": "https://example.com"}, "features": ["File import", "Multiple view modes", "Statistics display", "Download functionality"], "settings": {"theme": "light", "autoSave": true, "maxFileSize": "10MB", "supportedFormats": ["json"]}, "users": [{"id": 1, "username": "john_doe", "email": "<EMAIL>", "active": true, "roles": ["user", "editor"], "profile": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "age": 30, "preferences": {"notifications": true, "theme": "dark", "language": "en"}}}, {"id": 2, "username": "jane_smith", "email": "<EMAIL>", "active": false, "roles": ["user"], "profile": {"firstName": "<PERSON>", "lastName": "<PERSON>", "age": 25, "preferences": {"notifications": false, "theme": "light", "language": "es"}}}], "statistics": {"totalUsers": 2, "activeUsers": 1, "totalFiles": 156, "storageUsed": "2.3GB", "lastUpdated": "2024-01-15T10:30:00Z"}, "metadata": {"created": "2024-01-01T00:00:00Z", "modified": "2024-01-15T10:30:00Z", "tags": ["test", "sample", "demo"], "category": "development", "priority": "medium"}}